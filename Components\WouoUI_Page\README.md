# WouoUI_Page 用户界面库

> 一个轻量级、功能丰富的嵌入式 UI 库，支持多种页面类型和交互方式

## 📋 目录

- [项目概述](#项目概述)
- [文件结构](#文件结构)
- [快速开始](#快速开始)
- [基本概念](#基本概念)
- [初始化配置](#初始化配置)
- [页面创建](#页面创建)
- [页面跳转](#页面跳转)
- [用户输入处理](#用户输入处理)
- [弹窗使用](#弹窗使用)
- [高级配置](#高级配置)
- [主循环集成](#主循环集成)
- [完整示例](#完整示例)

## 🚀 项目概述

WouoUI_Page 是一个专为嵌入式系统设计的轻量级 UI 库，提供了丰富的页面类型和交互功能。支持标题页面、列表页面、波形页面以及多种弹窗组件，具有流畅的动画效果和灵活的消息处理机制。

### 主要特性

- 🎨 多种页面类型：Title、List、Wave 全屏页面
- 💬 丰富的弹窗组件：Msg、Conf、Val、Spin、ListWin
- ⚡ 非线性动画系统
- 🎯 灵活的消息队列机制
- 🔧 高度可配置的 UI 参数
- 📱 支持多种输入方式

## 📁 文件结构

WouoUI_Page 版源代码结构清晰，按功能模块划分：

| 代码文件 | 功能描述 |
|:---|:---|
| `WouoUI_conf.h` / `WouoUI_common.h` | 配置文件，定义屏幕宽高、字体等参数 / 内部通用函数和宏 |
| `WouoUI_font.c/h` | 字体文件和数组定义 |
| `WouoUI_anim.c/h` | 非线性动画系统实现 |
| `WouoUI_msg.c/h` | 输入消息队列的实现 |
| `WouoUI_graph.c/h` | 图形层，提供基本的图形和字符绘制函数 |
| **`WouoUI_page.c/h`** | **核心页面组件：Title、List、Wave 三种全屏页面** |
| **`WouoUI_win.c/h`** | **弹窗组件：Msg、Conf、Val、Spin、ListWin 五种弹窗** |
| **`WouoUI.c/h`** | **核心状态机和 UI 调度系统** |
| `WouoUI_user.c/h` | 用户 UI 示例文件，展示库的使用方法 |

## 🚀 快速开始

本教程旨在帮助已经完成 WouoUI_Page 库底层移植的用户快速上手应用层开发。

## 📖 基本概念

在开始使用 WouoUI_Page 之前，需要了解以下核心概念：

### 🖼️ 页面 (Page)
UI 的基本单元，分为两大类：
- **全屏页面**：`TitlePage`、`ListPage`、`WavePage`
- **弹窗页面**：`MsgWin`、`ConfWin`、`ValWin`、`SpinWin`、`ListWin`

每个页面都有自己的类型 (`PageType`) 和处理逻辑。

### ⚙️ 选项 (Option)
页面上可交互的元素，通常定义为 `Option` 结构体数组。包含以下属性：
- `text`：显示文本
- `content`：内容信息
- `val`：关联数值

**特殊字符功能**：`text` 字符串的第一个字符决定选项功能
- `+`：跳转页面
- `~`：打开滑动数值弹窗
- `@`：二值选框
- `!`：触发消息弹窗

### 📨 消息 (InputMsg)
用户输入操作的抽象表示：
- `msg_up` / `msg_down`：上下导航
- `msg_left` / `msg_right`：左右导航
- `msg_click`：确认选择
- `msg_return`：返回操作

### 🔄 回调函数 (CallBackFunc)
每个页面可绑定的事件处理函数，用于响应用户输入。

**函数原型**：
```c
bool CallBackFunc(const Page *cur_page_addr, InputMsg msg)
```

**返回值**：
- `true`：消息已处理，UI 不再执行默认操作
- `false`：消息未完全处理，UI 继续执行默认操作

## ⚙️ 初始化配置

在应用程序初始化部分，需要按以下步骤配置 WouoUI：

### 📋 初始化步骤

```c
#include "WouoUI.h"
#include "WouoUI_user.h"  // 包含用户定义的 UI 文件

/**
 * @brief 应用程序初始化函数
 * @note 在硬件初始化完成后调用
 */
void Application_Init() {
    // ... 其他硬件初始化代码 ...

    // 1️⃣ 选择默认 UI 配置（可选）
    WouoUI_SelectDefaultUI();

    // 2️⃣ 绑定屏幕刷新函数（必须）
    // 将 WouoUI 渲染的缓冲区数据显示到屏幕
    WouoUI_AttachSendBuffFun(Your_SendScreenBuff_Function);

    // 3️⃣ 清空屏幕缓冲区并首次发送（可选）
    WouoUI_BuffClear();
    WouoUI_BuffSend();

    // 4️⃣ 初始化所有 UI 页面（重要！）
    TestUI_Init();  // 调用用户定义的初始化函数

    // 5️⃣ 设置画笔颜色（可选）
    WouoUI_GraphSetPenColor(1);  // 1 通常代表前景色
}
```

### 🖥️ 屏幕刷新函数实现

```c
/**
 * @brief 屏幕刷新函数
 * @param buff WouoUI 渲染的数据缓冲区
 * @param size 缓冲区大小
 * @note 根据硬件平台和显示驱动实现
 */
void Your_SendScreenBuff_Function(uint8_t* buff, uint16_t size) {
    // 根据具体硬件实现数据传输
    // SPI 接口示例：
    // spi_send_data(buff, size);

    // I2C 接口示例：
    // i2c_send_data(buff, size);

    // 直接绘制示例：
    // lcd_draw_bitmap(0, 0, WOUOUI_BUFF_WIDTH, WOUOUI_BUFF_HEIGHT, buff);
}
```

> ⚠️ **注意**：`Your_SendScreenBuff_Function` 的具体实现取决于硬件平台和显示驱动。

## 🎨 页面创建

以创建一个简单的列表页面为例，展示完整的页面创建流程：

### 📝 页面定义

```c
#include "WouoUI.h"

// 1️⃣ 定义页面对象
ListPage my_list_page;

// 2️⃣ 定义页面选项数组
Option my_list_options[] = {
    {.text = (char*)"- My List Page"},              // 标题项，无功能
    {.text = (char*)"! Option 1"},                  // 点击弹出消息
    {.text = (char*)"! Option 2"},                  // 点击弹出消息
    {.text = (char*)"~ Adjust Value", .val = 50},   // 数值调节弹窗
    {.text = (char*)"+ Go to Main"},                // 跳转到主页面
};

// 3️⃣ 计算选项数量
#define MY_LIST_OPTION_NUM (sizeof(my_list_options) / sizeof(Option))
```

### 🔄 回调函数实现

```c
/**
 * @brief 列表页面回调函数
 * @param cur_page_addr 当前页面地址
 * @param msg 输入消息
 * @return true: 消息已处理; false: 继续默认处理
 */
bool MyListPage_CallBack(const Page *cur_page_addr, InputMsg msg) {
    if (msg == msg_click) {
        // 获取当前选中的选项
        Option* selected_option = WouoUI_ListTitlePageGetSelectOpt(cur_page_addr);

        // 根据选项文本执行不同操作
        if (strcmp(selected_option->text, "! Option 1") == 0) {
            // 弹出消息窗口
            WouoUI_MsgWinPageSetContent(&common_msg_page, (char*)"You clicked Option 1!");
            WouoUI_JumpToPage((PageAddr)cur_page_addr, &common_msg_page);

        } else if (strcmp(selected_option->text, "! Option 2") == 0) {
            WouoUI_MsgWinPageSetContent(&common_msg_page, (char*)"You clicked Option 2!");
            WouoUI_JumpToPage((PageAddr)cur_page_addr, &common_msg_page);

        } else if (strcmp(selected_option->text, "~ Adjust Value") == 0) {
            // 打开数值调节弹窗
            WouoUI_ValWinPageSetMinStepMax(&common_val_page, 0, 10, 100);
            WouoUI_JumpToPage((PageAddr)cur_page_addr, &common_val_page);

        } else if (strcmp(selected_option->text, "+ Go to Main") == 0) {
            // 跳转到主页面
            WouoUI_JumpToPage((PageAddr)cur_page_addr, &main_page);
        }

    } else if (msg == msg_return) {
        // 处理返回键
        WouoUI_JumpToPage((PageAddr)cur_page_addr, &main_page);
        return true;  // 消息已处理，不执行默认返回操作
    }

    return false;  // 继续执行默认操作（如列表上下移动）
}
```

### 🔧 页面初始化

```c
/**
 * @brief UI 初始化函数
 * @note 在 Application_Init() 中调用
 */
void TestUI_Init() {
    // ... 其他页面的初始化 ...

    // 4️⃣ 初始化列表页面
    WouoUI_ListPageInit(
        &my_list_page,           // 页面对象
        MY_LIST_OPTION_NUM,      // 选项数量
        my_list_options,         // 选项数组
        Setting_none,            // 页面设置
        MyListPage_CallBack      // 回调函数
    );

    // 5️⃣ 初始化共用弹窗页面
    WouoUI_MsgWinPageInit(&common_msg_page, NULL, false, 2, NULL);
    WouoUI_ValWinPageInit(&common_val_page, NULL, 0, 0, 100, 1, true, true, NULL);

    // ... 初始化其他弹窗 ...

    // 6️⃣ 设置主页面
    p_cur_ui->home_page = &main_page;
}
```

### 📌 关键要点

- **Option 数组**：定义页面内容和交互方式
- **Init 函数**：创建页面实例，配置参数和回调
- **回调函数**：处理页面逻辑的核心机制

## 🔄 页面跳转

使用 `WouoUI_JumpToPage()` 函数实现页面间的切换。

### 📋 函数说明

```c
WouoUI_JumpToPage(PageAddr self_page, PageAddr terminate_page)
```

**参数说明**：
- `self_page`：当前页面地址（通常传入回调函数的 `cur_page_addr`）
- `terminate_page`：目标页面地址

### 💡 使用示例

```c
// 在回调函数中实现页面跳转
WouoUI_JumpToPage((PageAddr)cur_page_addr, &target_page);
```

## 🎮 用户输入处理

WouoUI 提供了灵活的输入处理机制，支持自动和手动两种处理方式。

### 🤖 自动处理
- **默认行为**：WouoUI 自动处理页面的上、下、左、右移动逻辑
- **适用场景**：标准的导航操作无需额外编程

### 🎯 手动处理
- **回调函数**：捕获 `msg_click`、`msg_return` 等消息
- **自定义操作**：实现特定的业务逻辑

### 🔍 获取选中项

```c
// 获取列表或磁贴页面当前选中的选项
Option* selected_option = WouoUI_ListTitlePageGetSelectOpt(cur_page_addr);
```

> 💡 **提示**：不同页面类型可能有对应的获取函数

## 💬 弹窗使用

弹窗是一种特殊的页面类型，用于显示临时信息或获取用户输入。

### 🔧 弹窗使用流程

#### 1️⃣ 定义弹窗对象
```c
MsgWin common_msg_page;      // 消息弹窗
ConfWin common_conf_page;    // 确认弹窗
ValWin common_val_page;      // 数值弹窗
SpinWin common_spin_page;    // 旋转选择弹窗
ListWin common_list_page;    // 列表弹窗
```

#### 2️⃣ 初始化弹窗
```c
void TestUI_Init() {
    // 消息弹窗初始化
    WouoUI_MsgWinPageInit(&common_msg_page, NULL, false, 2, NULL);

    // 确认弹窗初始化
    WouoUI_ConfWinPageInit(&common_conf_page, NULL, NULL);

    // 数值弹窗初始化
    WouoUI_ValWinPageInit(&common_val_page, NULL, 0, 0, 100, 1, true, true, NULL);
}
```

#### 3️⃣ 触发弹窗
```c
// 在回调函数中跳转到弹窗页面
WouoUI_JumpToPage((PageAddr)cur_page_addr, &common_msg_page);
```

#### 4️⃣ 配置弹窗内容
```c
// 设置消息内容
WouoUI_MsgWinPageSetContent(&common_msg_page, (char*)"Hello World!");

// 设置数值范围
WouoUI_ValWinPageSetMinStepMax(&common_val_page, 0, 10, 100);
```

### 📊 弹窗结果处理

| 弹窗类型 | 结果获取方式 | 说明 |
|:---|:---|:---|
| **ConfWin** | 检查 `conf_ret` 成员 | 获取用户选择（Yes/No） |
| **ValWin/SpinWin** | 检查 `val` 成员 | 获取调节后的数值 |
| **ListWin** | 检查 `sel_str_index` | 获取选择的列表项索引 |

> 💡 **自动回写**：数值弹窗可设置 `auto_set_bg_opt` 为 `true`，自动将结果写回触发选项的 `val` 值

## ⚙️ 高级配置

通过修改全局变量 `g_default_ui_para` 可以调整 UI 的视觉和行为参数。

### 🎨 可配置参数

| 参数类型 | 配置项 | 说明 |
|:---|:---|:---|
| **动画参数** | `ani_param[LIST_ANI]` | 列表动画速度（数值越大越慢） |
| **循环参数** | `loop_param[TILE_LOOP]` | 磁贴页面循环模式（1=开启，0=关闭） |
| **背景参数** | 弹窗背景模糊程度 | 控制弹窗背景效果 |

### 💡 配置示例

```c
void TestUI_Init() {
    // ⚠️ 在页面初始化之前配置参数

    // 减慢列表动画速度
    g_default_ui_para.ani_param[LIST_ANI] = 300;

    // 开启磁贴页面循环模式
    g_default_ui_para.loop_param[TILE_LOOP] = 1;

    // ... 然后进行页面初始化 ...
}
```

> ⚠️ **注意**：参数修改应在 `TestUI_Init()` 函数中进行，且要在页面初始化之前。

### 8. 主循环集成

在你的应用程序主循环 (例如 `while(1)`) 中，你需要：

1.  **获取用户输入**: 检测按键或其他输入设备，并将用户的操作转换为 `InputMsg`。
2.  **发送消息**: 将获取到的 `InputMsg` 发送到 WouoUI 的消息队列中。
3.  **处理 UI**: 调用 `WouoUI_Proc()` 函数，传入两次调用之间的时间间隔 (毫秒)，驱动 UI 状态机运行、处理消息、执行动画和渲染。

```c
#include "WouoUI.h"

uint32_t last_tick = 0; // 用于计算时间间隔

int main(void) {
    Application_Init(); // 执行之前的初始化

    while (1) {
        // 1. 获取用户输入 (你需要实现这部分)
        InputMsg current_msg = Get_User_Input(); // 返回 msg_none 如果无输入

        // 2. 发送消息 (如果有输入)
        if (current_msg != msg_none) {
            WouoUI_SendMsg(current_msg);
        }

        // 3. 计算时间间隔
        uint32_t current_tick = Your_GetTick_Function(); // 获取当前系统时间 (毫秒)
        uint16_t delta_time = current_tick - last_tick;
        last_tick = current_tick;

        // 4. 处理 UI
        WouoUI_Proc(delta_time);

        // ... 其他需要在主循环中执行的任务 ...
        // 例如： Your_LowPower_Handler();
    }
}

// 你需要实现的获取用户输入的函数示例
InputMsg Get_User_Input() {
    if (Is_KeyUp_Pressed()) return msg_up;
    if (Is_KeyDown_Pressed()) return msg_down;
    if (Is_KeyLeft_Pressed()) return msg_left;
    if (Is_KeyRight_Pressed()) return msg_right;
    if (Is_KeyEnter_Pressed()) return msg_click;
    if (Is_KeyBack_Pressed()) return msg_return;
    return msg_none; // 没有按键按下
}

// 你需要实现的获取系统 Tick 的函数示例
uint32_t Your_GetTick_Function() {
    // 返回 HAL_GetTick() 或其他获取毫秒级时间戳的函数
    return HAL_GetTick();
}
```

### 9. 简单示例回顾

结合以上步骤，一个最简单的应用流程：

1.  **硬件和 WouoUI 初始化**: `Application_Init()` 中完成。
2.  **定义页面和选项**: 在 `WouoUI_user.c/.h` 中定义 `main_page`、`my_list_page` 等，以及它们的 `Option` 数组和回调函数。
3.  **初始化页面**: 在 `TestUI_Init()` 中调用 `WouoUI_TitlePageInit()`, `WouoUI_ListPageInit()` 等。
4.  **主循环**:
    *   检测按键输入 -> `Get_User_Input()`
    *   发送消息 -> `WouoUI_SendMsg()`
    *   处理 UI -> `WouoUI_Proc()`
5.  **页面逻辑**: 在页面的回调函数中处理 `msg_click` 和 `msg_return`，使用 `WouoUI_JumpToPage()` 进行页面切换或弹出窗口。

希望这份教程能帮助你快速上手 WouoUI_Page 的应用层开发！如有疑问，请参考 `WouoUI_user.c` 中的示例代码，它展示了更复杂的用法。

