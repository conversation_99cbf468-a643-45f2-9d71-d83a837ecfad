# 配置管理模块 (Config Manager)

> 灵活的系统配置管理与持久化解决方案

## 📋 目录

- [模块概述](#模块概述)
- [配置架构](#配置架构)
- [配置格式](#配置格式)
- [API 接口](#api-接口)
- [使用示例](#使用示例)
- [配置验证](#配置验证)
- [默认配置](#默认配置)
- [最佳实践](#最佳实践)

## 🚀 模块概述

配置管理模块提供统一的系统配置管理接口，支持 INI 格式配置文件的读写、参数验证、默认值管理和动态配置更新。采用分层配置架构，确保配置的灵活性和可维护性。

### 🎯 设计目标

- **标准格式**：支持 INI 标准配置文件格式
- **类型安全**：强类型参数访问和验证
- **默认值**：完善的默认配置机制
- **动态更新**：运行时配置修改和生效
- **持久化**：配置数据的可靠存储和恢复

## 🏗️ 配置架构

### 分层配置结构

```
┌─────────────────────────────────────────────────────────┐
│                    应用配置层                            │
├─────────────────────────────────────────────────────────┤
│  系统配置 │ 设备配置 │ 用户配置 │ 网络配置 │ 显示配置    │
├─────────────────────────────────────────────────────────┤
│                    配置管理层                            │
├─────────────────────────────────────────────────────────┤
│  解析器   │ 验证器   │ 缓存管理 │ 变更通知 │ 备份恢复    │
├─────────────────────────────────────────────────────────┤
│                    存储接口层                            │
├─────────────────────────────────────────────────────────┤
│  文件系统 │ Flash存储│ EEPROM  │ 网络存储 │ 云端同步    │
└─────────────────────────────────────────────────────────┘
```

### 配置分类

| 配置类别 | 存储位置 | 更新频率 | 重要性 |
|:---|:---|:---|:---|
| **系统配置** | 内部Flash | 低 | 关键 |
| **设备配置** | 外部Flash | 中 | 重要 |
| **用户配置** | SD卡 | 高 | 普通 |
| **临时配置** | RAM | 实时 | 临时 |

## 📄 配置格式

### INI 文件格式

```ini
; CIMC HAL V4 系统配置文件
; 版本: 1.0
; 更新时间: 2024-01-01

[System]
; 系统基本配置
device_id=CIMC001              ; 设备ID
firmware_version=4.0.1         ; 固件版本
sampling_cycle=10              ; 采样周期(秒)
data_storage_enable=1          ; 数据存储使能
debug_mode=0                   ; 调试模式

[Hardware]
; 硬件相关配置
adc_resolution=12              ; ADC分辨率
adc_vref=3.3                   ; ADC参考电压
uart_baudrate=115200           ; 串口波特率
i2c_speed=400000               ; I2C速度
spi_speed=1000000              ; SPI速度

[Sampling]
; 采样配置
channel_count=4                ; 通道数量
channel_enable=1,1,1,0         ; 通道使能状态
voltage_range=0.0,5.0          ; 电压范围
current_range=0.0,1.0          ; 电流范围
temperature_range=-40,85       ; 温度范围

[Limits]
; 限值配置
voltage_max=4.5                ; 电压上限
voltage_min=0.5                ; 电压下限
current_max=0.8                ; 电流上限
temperature_max=80             ; 温度上限
pressure_max=1000              ; 压力上限

[Display]
; 显示配置
brightness=80                  ; 亮度(0-100)
contrast=100                   ; 对比度(0-100)
auto_off_time=300              ; 自动关闭时间(秒)
language=zh_CN                 ; 语言设置
theme=default                  ; 主题设置

[Communication]
; 通信配置
uart_enable=1                  ; 串口使能
uart_parity=none               ; 校验位
uart_stopbits=1                ; 停止位
tcp_port=8080                  ; TCP端口
udp_port=8081                  ; UDP端口

[Storage]
; 存储配置
flash_enable=1                 ; Flash存储使能
sd_enable=1                    ; SD卡使能
backup_enable=1                ; 备份使能
compression_enable=1           ; 压缩使能
retention_days=30              ; 数据保留天数

[Security]
; 安全配置
password_enable=0              ; 密码使能
encryption_enable=0            ; 加密使能
access_level=1                 ; 访问级别
session_timeout=1800           ; 会话超时(秒)
```

## 🔧 API 接口

### 初始化接口

```c
/**
 * @brief 初始化配置管理器
 * @return int 0-成功, 其他-错误码
 */
int config_manager_init(void);

/**
 * @brief 加载配置文件
 * @param filename 配置文件名
 * @return int 0-成功, 其他-错误码
 */
int config_manager_load(const char* filename);

/**
 * @brief 保存配置文件
 * @param filename 配置文件名
 * @return int 0-成功, 其他-错误码
 */
int config_manager_save(const char* filename);
```

### 参数访问接口

```c
/**
 * @brief 获取整数参数
 * @param section 节名称
 * @param key 键名称
 * @param default_value 默认值
 * @return int 参数值
 */
int config_get_int(const char* section, const char* key, int default_value);

/**
 * @brief 获取浮点参数
 * @param section 节名称
 * @param key 键名称
 * @param default_value 默认值
 * @return float 参数值
 */
float config_get_float(const char* section, const char* key, float default_value);

/**
 * @brief 获取字符串参数
 * @param section 节名称
 * @param key 键名称
 * @param buffer 缓冲区
 * @param size 缓冲区大小
 * @param default_value 默认值
 * @return int 实际长度
 */
int config_get_string(const char* section, const char* key, char* buffer, int size, const char* default_value);

/**
 * @brief 获取布尔参数
 * @param section 节名称
 * @param key 键名称
 * @param default_value 默认值
 * @return int 1-true, 0-false
 */
int config_get_bool(const char* section, const char* key, int default_value);
```

### 参数设置接口

```c
/**
 * @brief 设置整数参数
 * @param section 节名称
 * @param key 键名称
 * @param value 参数值
 * @return int 0-成功, 其他-错误码
 */
int config_set_int(const char* section, const char* key, int value);

/**
 * @brief 设置浮点参数
 * @param section 节名称
 * @param key 键名称
 * @param value 参数值
 * @return int 0-成功, 其他-错误码
 */
int config_set_float(const char* section, const char* key, float value);

/**
 * @brief 设置字符串参数
 * @param section 节名称
 * @param key 键名称
 * @param value 参数值
 * @return int 0-成功, 其他-错误码
 */
int config_set_string(const char* section, const char* key, const char* value);

/**
 * @brief 设置布尔参数
 * @param section 节名称
 * @param key 键名称
 * @param value 参数值
 * @return int 0-成功, 其他-错误码
 */
int config_set_bool(const char* section, const char* key, int value);
```

### 高级接口

```c
/**
 * @brief 重置为默认配置
 * @return int 0-成功, 其他-错误码
 */
int config_reset_defaults(void);

/**
 * @brief 验证配置有效性
 * @return int 0-有效, 其他-无效
 */
int config_validate(void);

/**
 * @brief 备份当前配置
 * @param backup_name 备份名称
 * @return int 0-成功, 其他-错误码
 */
int config_backup(const char* backup_name);

/**
 * @brief 恢复配置
 * @param backup_name 备份名称
 * @return int 0-成功, 其他-错误码
 */
int config_restore(const char* backup_name);

/**
 * @brief 注册配置变更回调
 * @param callback 回调函数
 * @return int 0-成功, 其他-错误码
 */
int config_register_callback(config_change_callback_t callback);
```

## 💡 使用示例

### 基本配置操作

```c
#include "config_manager.h"

void basic_config_example(void) {
    // 初始化配置管理器
    if (config_manager_init() != 0) {
        printf("Config manager initialization failed\n");
        return;
    }
    
    // 加载配置文件
    if (config_manager_load("config.ini") != 0) {
        printf("Failed to load config file, using defaults\n");
        config_reset_defaults();
    }
    
    // 读取配置参数
    int sampling_cycle = config_get_int("System", "sampling_cycle", 10);
    float voltage_max = config_get_float("Limits", "voltage_max", 4.5f);
    
    char device_id[32];
    config_get_string("System", "device_id", device_id, sizeof(device_id), "CIMC001");
    
    int debug_mode = config_get_bool("System", "debug_mode", 0);
    
    printf("Device ID: %s\n", device_id);
    printf("Sampling Cycle: %d seconds\n", sampling_cycle);
    printf("Voltage Max: %.1f V\n", voltage_max);
    printf("Debug Mode: %s\n", debug_mode ? "Enabled" : "Disabled");
}
```

### 动态配置更新

```c
void dynamic_config_example(void) {
    // 运行时修改配置
    config_set_int("System", "sampling_cycle", 5);
    config_set_float("Limits", "voltage_max", 5.0f);
    config_set_bool("System", "debug_mode", 1);
    
    // 保存配置到文件
    if (config_manager_save("config.ini") == 0) {
        printf("Configuration saved successfully\n");
    }
    
    // 验证配置有效性
    if (config_validate() == 0) {
        printf("Configuration is valid\n");
    } else {
        printf("Configuration validation failed\n");
        config_reset_defaults();
    }
}
```

### 配置结构化访问

```c
// 定义配置结构体
typedef struct {
    char device_id[32];
    int sampling_cycle;
    float voltage_max;
    float voltage_min;
    int debug_mode;
} system_config_t;

void structured_config_example(void) {
    system_config_t config;
    
    // 加载结构化配置
    load_system_config(&config);
    
    // 使用配置
    printf("System Configuration:\n");
    printf("  Device ID: %s\n", config.device_id);
    printf("  Sampling Cycle: %d\n", config.sampling_cycle);
    printf("  Voltage Range: %.1f - %.1f V\n", config.voltage_min, config.voltage_max);
    printf("  Debug Mode: %s\n", config.debug_mode ? "On" : "Off");
    
    // 修改配置
    config.sampling_cycle = 15;
    config.voltage_max = 4.8f;
    
    // 保存结构化配置
    save_system_config(&config);
}

void load_system_config(system_config_t* config) {
    config_get_string("System", "device_id", config->device_id, 32, "CIMC001");
    config->sampling_cycle = config_get_int("System", "sampling_cycle", 10);
    config->voltage_max = config_get_float("Limits", "voltage_max", 4.5f);
    config->voltage_min = config_get_float("Limits", "voltage_min", 0.5f);
    config->debug_mode = config_get_bool("System", "debug_mode", 0);
}

void save_system_config(const system_config_t* config) {
    config_set_string("System", "device_id", config->device_id);
    config_set_int("System", "sampling_cycle", config->sampling_cycle);
    config_set_float("Limits", "voltage_max", config->voltage_max);
    config_set_float("Limits", "voltage_min", config->voltage_min);
    config_set_bool("System", "debug_mode", config->debug_mode);
    
    config_manager_save("config.ini");
}
```

### 配置变更监听

```c
// 配置变更回调函数
void config_change_handler(const char* section, const char* key, const char* old_value, const char* new_value) {
    printf("Config changed: [%s]%s = %s -> %s\n", section, key, old_value, new_value);
    
    // 根据配置变更执行相应操作
    if (strcmp(section, "System") == 0 && strcmp(key, "sampling_cycle") == 0) {
        int new_cycle = atoi(new_value);
        sampling_set_cycle(new_cycle);
        printf("Sampling cycle updated to %d seconds\n", new_cycle);
    }
    
    if (strcmp(section, "Display") == 0 && strcmp(key, "brightness") == 0) {
        int brightness = atoi(new_value);
        display_set_brightness(brightness);
        printf("Display brightness updated to %d%%\n", brightness);
    }
}

void config_monitoring_example(void) {
    // 注册配置变更回调
    config_register_callback(config_change_handler);
    
    // 模拟配置变更
    config_set_int("System", "sampling_cycle", 20);
    config_set_int("Display", "brightness", 90);
    
    // 配置变更会自动触发回调函数
}
```

## ✅ 配置验证

### 参数范围验证

```c
// 配置验证规则
typedef struct {
    const char* section;
    const char* key;
    config_type_t type;
    union {
        struct { int min, max; } int_range;
        struct { float min, max; } float_range;
        struct { int min_len, max_len; } string_range;
    } range;
} config_rule_t;

// 定义验证规则
static const config_rule_t validation_rules[] = {
    {"System", "sampling_cycle", CONFIG_TYPE_INT, .range.int_range = {1, 3600}},
    {"Limits", "voltage_max", CONFIG_TYPE_FLOAT, .range.float_range = {0.0f, 10.0f}},
    {"Limits", "voltage_min", CONFIG_TYPE_FLOAT, .range.float_range = {0.0f, 10.0f}},
    {"Display", "brightness", CONFIG_TYPE_INT, .range.int_range = {0, 100}},
    {"System", "device_id", CONFIG_TYPE_STRING, .range.string_range = {1, 31}},
};

// 验证配置参数
int validate_config_parameter(const char* section, const char* key, const char* value) {
    for (int i = 0; i < sizeof(validation_rules) / sizeof(config_rule_t); i++) {
        const config_rule_t* rule = &validation_rules[i];
        
        if (strcmp(rule->section, section) == 0 && strcmp(rule->key, key) == 0) {
            switch (rule->type) {
                case CONFIG_TYPE_INT: {
                    int val = atoi(value);
                    return (val >= rule->range.int_range.min && val <= rule->range.int_range.max) ? 0 : -1;
                }
                case CONFIG_TYPE_FLOAT: {
                    float val = atof(value);
                    return (val >= rule->range.float_range.min && val <= rule->range.float_range.max) ? 0 : -1;
                }
                case CONFIG_TYPE_STRING: {
                    int len = strlen(value);
                    return (len >= rule->range.string_range.min_len && len <= rule->range.string_range.max_len) ? 0 : -1;
                }
            }
        }
    }
    return 0;  // 未找到规则，认为有效
}
```
