# 采样控制模块 (Sampling Control)

> 高精度数据采集与实时监控系统

## 📋 目录

- [模块概述](#模块概述)
- [功能特性](#功能特性)
- [数据结构](#数据结构)
- [API 接口](#api-接口)
- [使用示例](#使用示例)
- [配置参数](#配置参数)
- [状态机制](#状态机制)
- [错误处理](#错误处理)

## 🚀 模块概述

采样控制模块是系统的核心数据采集组件，负责管理 ADC 采样、数据处理、状态监控和异常检测。支持多种采样周期配置，提供实时数据监控和超限报警功能。

### 🎯 设计目标

- **高精度采样**：12位 ADC 分辨率，支持多通道同步采样
- **灵活配置**：可配置采样周期和触发条件
- **实时监控**：实时数据处理和状态反馈
- **异常检测**：自动检测数据异常和系统故障
- **低功耗设计**：优化的采样策略，降低系统功耗

## ✨ 功能特性

### 📊 数据采集
- **采样精度**: 12位 ADC，0.1% 精度
- **采样速率**: 最高 1MHz，可配置
- **通道数量**: 支持多达 16 个模拟通道
- **数据格式**: IEEE 754 单精度浮点

### ⏱️ 时间控制
- **采样周期**: 5s/10s/15s 可选
- **定时精度**: ±1ms 精度
- **同步采样**: 多通道同步触发
- **时间戳**: RTC 时间戳记录

### 🚨 监控报警
- **超限检测**: 可配置上下限阈值
- **趋势分析**: 数据变化趋势监控
- **状态指示**: LED 状态指示灯
- **报警输出**: 声光报警信号

## 📊 数据结构

### 采样状态枚举
```c
typedef enum {
    SAMPLING_IDLE = 0,      // 空闲状态
    SAMPLING_ACTIVE = 1     // 采样活动状态
} sampling_state_t;
```

### 采样周期枚举
```c
typedef enum {
    CYCLE_5S = 5,           // 5秒周期
    CYCLE_10S = 10,         // 10秒周期
    CYCLE_15S = 15          // 15秒周期
} sampling_cycle_t;
```

### 控制结构体
```c
typedef struct {
    sampling_state_t state;     // 当前状态
    sampling_cycle_t cycle;     // 采样周期
    uint32_t last_sample_time;  // 上次采样时间
    uint32_t led_blink_time;    // LED闪烁时间
    uint8_t led_blink_state;    // LED闪烁状态
} sampling_control_t;
```

### 状态码枚举
```c
typedef enum {
    SAMPLING_OK = 0,        // 操作成功
    SAMPLING_ERROR = 1,     // 操作失败
    SAMPLING_INVALID = 2,   // 参数无效
    SAMPLING_OVERLIMIT = 3  // 数据超限
} sampling_status_t;
```

## 🔧 API 接口

### 基本控制接口

#### 初始化函数
```c
/**
 * @brief 初始化采样控制模块
 * @return sampling_status_t 初始化状态
 * @note 必须在使用其他接口前调用
 */
sampling_status_t sampling_init(void);
```

#### 启动/停止控制
```c
/**
 * @brief 启动采样
 * @return sampling_status_t 操作状态
 */
sampling_status_t sampling_start(void);

/**
 * @brief 停止采样
 * @return sampling_status_t 操作状态
 */
sampling_status_t sampling_stop(void);
```

#### 参数配置
```c
/**
 * @brief 设置采样周期
 * @param cycle 采样周期
 * @return sampling_status_t 操作状态
 */
sampling_status_t sampling_set_cycle(sampling_cycle_t cycle);
```

### 状态查询接口

#### 状态获取
```c
/**
 * @brief 获取采样状态
 * @return sampling_state_t 当前状态
 */
sampling_state_t sampling_get_state(void);

/**
 * @brief 获取采样周期
 * @return sampling_cycle_t 当前周期
 */
sampling_cycle_t sampling_get_cycle(void);
```

#### 数据读取
```c
/**
 * @brief 获取当前电压值
 * @return float 电压值(V)
 */
float sampling_get_voltage(void);

/**
 * @brief 检查是否超限
 * @return uint8_t 1-超限, 0-正常
 */
uint8_t sampling_check_overlimit(void);
```

### 任务处理接口

#### 主任务函数
```c
/**
 * @brief 采样任务处理函数
 * @note 在主循环中周期性调用
 */
void sampling_task(void);
```

#### 辅助函数
```c
/**
 * @brief 检查是否需要采样
 * @return uint8_t 1-需要, 0-不需要
 */
uint8_t sampling_should_sample(void);

/**
 * @brief 更新LED闪烁状态
 */
void sampling_update_led_blink(void);

/**
 * @brief 获取LED闪烁状态
 * @return uint8_t LED状态
 */
uint8_t sampling_get_led_blink_state(void);

/**
 * @brief 处理数据存储
 * @param voltage 电压值
 * @param is_overlimit 超限标志
 */
void sampling_handle_data_storage(float voltage, uint8_t is_overlimit);
```

## 💡 使用示例

### 基本使用流程

```c
#include "sampling_control.h"

int main(void) {
    // 1. 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // 2. 初始化采样模块
    if (sampling_init() != SAMPLING_OK) {
        // 处理初始化错误
        Error_Handler();
    }
    
    // 3. 配置采样参数
    sampling_set_cycle(CYCLE_10S);  // 设置10秒采样周期
    
    // 4. 启动采样
    if (sampling_start() != SAMPLING_OK) {
        // 处理启动错误
        Error_Handler();
    }
    
    // 5. 主循环
    while (1) {
        sampling_task();  // 处理采样任务
        HAL_Delay(10);   // 10ms延时
    }
}
```

### 高级使用示例

```c
// 多通道采样示例
void multi_channel_sampling_example(void) {
    float voltage_ch1, voltage_ch2;
    uint8_t overlimit_status;
    
    // 检查采样状态
    if (sampling_get_state() == SAMPLING_ACTIVE) {
        // 读取多通道数据
        voltage_ch1 = sampling_get_voltage();
        
        // 检查超限状态
        overlimit_status = sampling_check_overlimit();
        
        if (overlimit_status) {
            // 处理超限情况
            printf("Warning: Voltage overlimit detected!\n");
            
            // 可以触发报警或保护动作
            trigger_alarm();
        }
        
        // 记录数据
        sampling_handle_data_storage(voltage_ch1, overlimit_status);
    }
}

// 动态配置示例
void dynamic_config_example(void) {
    sampling_cycle_t current_cycle;
    
    // 获取当前配置
    current_cycle = sampling_get_cycle();
    
    // 根据条件动态调整采样周期
    if (system_load_high()) {
        // 系统负载高时降低采样频率
        sampling_set_cycle(CYCLE_15S);
    } else if (precision_required()) {
        // 需要高精度时提高采样频率
        sampling_set_cycle(CYCLE_5S);
    }
}
```

### 错误处理示例

```c
void error_handling_example(void) {
    sampling_status_t status;
    
    // 启动采样并检查结果
    status = sampling_start();
    
    switch (status) {
        case SAMPLING_OK:
            printf("Sampling started successfully\n");
            break;
            
        case SAMPLING_ERROR:
            printf("Failed to start sampling\n");
            // 尝试重新初始化
            sampling_init();
            break;
            
        case SAMPLING_INVALID:
            printf("Invalid sampling parameters\n");
            // 重置为默认配置
            sampling_set_cycle(CYCLE_10S);
            break;
            
        default:
            printf("Unknown error occurred\n");
            break;
    }
}
```

## ⚙️ 配置参数

### 编译时配置

在 `sampling_control.h` 中定义的配置参数：

```c
// 采样配置
#define SAMPLING_BUFFER_SIZE    1024    // 采样缓冲区大小
#define SAMPLING_CHANNELS       4       // 采样通道数量
#define SAMPLING_RESOLUTION     12      // ADC分辨率(位)
#define SAMPLING_VREF           3.3f    // 参考电压(V)

// 超限检测配置
#define VOLTAGE_UPPER_LIMIT     3.0f    // 电压上限(V)
#define VOLTAGE_LOWER_LIMIT     0.5f    // 电压下限(V)
#define OVERLIMIT_FILTER_COUNT  3       // 超限滤波次数

// LED指示配置
#define LED_BLINK_PERIOD        500     // LED闪烁周期(ms)
#define LED_BLINK_DUTY          50      // LED闪烁占空比(%)
```

### 运行时配置

通过配置文件 `config.ini` 设置的参数：

```ini
[Sampling]
cycle=10                    # 采样周期(秒)
enable=1                    # 采样使能
channels=4                  # 通道数量
resolution=12               # ADC分辨率

[Limits]
voltage_max=3.0             # 电压上限
voltage_min=0.5             # 电压下限
current_max=1.0             # 电流上限
temperature_max=85          # 温度上限

[Display]
led_enable=1                # LED指示使能
led_brightness=80           # LED亮度
alarm_enable=1              # 报警使能
```
