# 数据存储模块 (Data Storage)

> 多介质数据存储与管理系统

## 📋 目录

- [模块概述](#模块概述)
- [存储架构](#存储架构)
- [数据格式](#数据格式)
- [API 接口](#api-接口)
- [使用示例](#使用示例)
- [存储策略](#存储策略)
- [数据恢复](#数据恢复)
- [性能优化](#性能优化)

## 🚀 模块概述

数据存储模块提供多层次、多介质的数据存储解决方案，支持实时数据记录、历史数据查询、系统日志管理和配置数据持久化。采用分层存储架构，确保数据安全性和访问效率。

### 🎯 设计目标

- **多介质支持**：内部 Flash、外部 Flash、SD 卡多种存储介质
- **数据安全**：多重备份、校验机制、断电保护
- **高效访问**：缓存机制、索引优化、快速检索
- **容量管理**：自动清理、循环覆盖、压缩存储
- **实时性能**：低延迟写入、后台整理、异步操作

## 🏗️ 存储架构

### 分层存储结构

```
┌─────────────────────────────────────────────────────────┐
│                    应用数据层                            │
├─────────────────────────────────────────────────────────┤
│  采样数据 │ 系统日志 │ 配置参数 │ 用户设置 │ 校准数据    │
├─────────────────────────────────────────────────────────┤
│                    存储管理层                            │
├─────────────────────────────────────────────────────────┤
│  缓存管理 │ 索引管理 │ 空间管理 │ 备份管理 │ 压缩管理    │
├─────────────────────────────────────────────────────────┤
│                    驱动接口层                            │
├─────────────────────────────────────────────────────────┤
│  Flash驱动│ SD卡驱动 │ FATFS   │ LittleFS │ 原始访问    │
├─────────────────────────────────────────────────────────┤
│                    硬件存储层                            │
└─────────────────────────────────────────────────────────┘
```

### 存储介质分配

| 存储介质 | 容量 | 用途 | 特点 |
|:---|:---|:---|:---|
| **内部 Flash** | 2MB | 系统配置、关键数据 | 高可靠性、快速访问 |
| **外部 Flash** | 16MB | 采样数据、日志文件 | 大容量、可擦写 |
| **SD 卡** | 32GB | 历史数据、备份文件 | 超大容量、可移动 |
| **RAM 缓存** | 64KB | 临时缓存、快速访问 | 高速访问、易失性 |

## 📊 数据格式

### 采样数据格式

```c
// 采样数据记录结构
typedef struct {
    uint32_t timestamp;         // 时间戳 (Unix时间)
    uint16_t sequence;          // 序列号
    uint8_t channel_count;      // 通道数量
    uint8_t status;             // 状态标志
    float voltage[MAX_CHANNELS]; // 电压值数组
    uint16_t checksum;          // 校验和
} __attribute__((packed)) sample_record_t;
```

### 日志数据格式

```c
// 日志记录结构
typedef struct {
    uint32_t timestamp;         // 时间戳
    uint8_t level;              // 日志级别
    uint8_t module;             // 模块ID
    uint16_t event_id;          // 事件ID
    char message[LOG_MSG_SIZE]; // 日志消息
    uint16_t checksum;          // 校验和
} __attribute__((packed)) log_record_t;
```

### 配置数据格式

```c
// 配置数据结构
typedef struct {
    uint32_t magic;             // 魔数标识
    uint16_t version;           // 版本号
    uint16_t size;              // 数据大小
    uint8_t data[CONFIG_SIZE];  // 配置数据
    uint32_t crc32;             // CRC32校验
} __attribute__((packed)) config_record_t;
```

## 🔧 API 接口

### 初始化接口

```c
/**
 * @brief 初始化数据存储模块
 * @return int 0-成功, 其他-错误码
 */
int data_storage_init(void);

/**
 * @brief 格式化存储介质
 * @param storage_type 存储类型
 * @return int 0-成功, 其他-错误码
 */
int data_storage_format(storage_type_t storage_type);
```

### 采样数据接口

```c
/**
 * @brief 写入采样数据
 * @param voltage 电压值
 * @param is_overlimit 超限标志
 * @return int 0-成功, 其他-错误码
 */
int data_storage_write_sample(float voltage, uint8_t is_overlimit);

/**
 * @brief 批量写入采样数据
 * @param samples 采样数据数组
 * @param count 数据数量
 * @return int 写入的数据数量
 */
int data_storage_write_samples(const sample_record_t* samples, uint32_t count);

/**
 * @brief 读取采样数据
 * @param buffer 数据缓冲区
 * @param start_time 开始时间
 * @param end_time 结束时间
 * @return int 读取的数据数量
 */
int data_storage_read_samples(sample_record_t* buffer, uint32_t start_time, uint32_t end_time);
```

### 日志数据接口

```c
/**
 * @brief 写入日志信息
 * @param level 日志级别
 * @param module 模块ID
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return int 0-成功, 其他-错误码
 */
int data_storage_write_log(uint8_t level, uint8_t module, const char* format, ...);

/**
 * @brief 读取日志信息
 * @param buffer 日志缓冲区
 * @param size 缓冲区大小
 * @param start_time 开始时间
 * @param end_time 结束时间
 * @return int 读取的字节数
 */
int data_storage_read_logs(char* buffer, uint32_t size, uint32_t start_time, uint32_t end_time);
```

### 配置数据接口

```c
/**
 * @brief 保存配置数据
 * @param config_id 配置ID
 * @param data 配置数据
 * @param size 数据大小
 * @return int 0-成功, 其他-错误码
 */
int data_storage_save_config(uint16_t config_id, const void* data, uint16_t size);

/**
 * @brief 加载配置数据
 * @param config_id 配置ID
 * @param data 数据缓冲区
 * @param size 缓冲区大小
 * @return int 实际读取的字节数
 */
int data_storage_load_config(uint16_t config_id, void* data, uint16_t size);
```

### 存储管理接口

```c
/**
 * @brief 获取存储空间信息
 * @param storage_type 存储类型
 * @param info 存储信息结构
 * @return int 0-成功, 其他-错误码
 */
int data_storage_get_info(storage_type_t storage_type, storage_info_t* info);

/**
 * @brief 清理存储空间
 * @param storage_type 存储类型
 * @param keep_days 保留天数
 * @return int 清理的字节数
 */
int data_storage_cleanup(storage_type_t storage_type, uint16_t keep_days);

/**
 * @brief 同步缓存数据
 * @return int 0-成功, 其他-错误码
 */
int data_storage_sync(void);
```

## 💡 使用示例

### 基本数据存储

```c
#include "data_storage.h"

void basic_storage_example(void) {
    float voltage = 3.14f;
    uint8_t overlimit = 0;
    
    // 初始化存储模块
    if (data_storage_init() != 0) {
        printf("Storage initialization failed\n");
        return;
    }
    
    // 写入采样数据
    if (data_storage_write_sample(voltage, overlimit) == 0) {
        printf("Sample data saved successfully\n");
    }
    
    // 写入日志信息
    data_storage_write_log(LOG_LEVEL_INFO, MODULE_SAMPLING, 
                          "Voltage: %.2fV, Status: %s", 
                          voltage, overlimit ? "OVERLIMIT" : "NORMAL");
}
```

### 批量数据操作

```c
void batch_operation_example(void) {
    sample_record_t samples[100];
    uint32_t count = 0;
    
    // 准备批量数据
    for (int i = 0; i < 100; i++) {
        samples[i].timestamp = HAL_GetTick() + i * 1000;
        samples[i].sequence = i;
        samples[i].channel_count = 4;
        samples[i].voltage[0] = 3.0f + (float)i * 0.01f;
        // ... 填充其他字段
    }
    
    // 批量写入
    count = data_storage_write_samples(samples, 100);
    printf("Written %d samples\n", count);
    
    // 批量读取
    sample_record_t read_buffer[50];
    uint32_t start_time = HAL_GetTick() - 60000;  // 1分钟前
    uint32_t end_time = HAL_GetTick();            // 现在
    
    count = data_storage_read_samples(read_buffer, start_time, end_time);
    printf("Read %d samples\n", count);
}
```

### 配置数据管理

```c
typedef struct {
    uint16_t sampling_cycle;
    uint8_t channel_enable[8];
    float calibration_factor[8];
    char device_name[32];
} system_config_t;

void config_management_example(void) {
    system_config_t config;
    
    // 设置默认配置
    config.sampling_cycle = 10;
    memset(config.channel_enable, 1, sizeof(config.channel_enable));
    for (int i = 0; i < 8; i++) {
        config.calibration_factor[i] = 1.0f;
    }
    strcpy(config.device_name, "CIMC_Device_001");
    
    // 保存配置
    if (data_storage_save_config(CONFIG_ID_SYSTEM, &config, sizeof(config)) == 0) {
        printf("Configuration saved\n");
    }
    
    // 加载配置
    system_config_t loaded_config;
    int size = data_storage_load_config(CONFIG_ID_SYSTEM, &loaded_config, sizeof(loaded_config));
    if (size == sizeof(loaded_config)) {
        printf("Configuration loaded: %s\n", loaded_config.device_name);
    }
}
```

### 存储空间管理

```c
void storage_management_example(void) {
    storage_info_t info;
    
    // 获取存储信息
    if (data_storage_get_info(STORAGE_TYPE_FLASH, &info) == 0) {
        printf("Flash Storage:\n");
        printf("  Total: %d KB\n", info.total_size / 1024);
        printf("  Used: %d KB\n", info.used_size / 1024);
        printf("  Free: %d KB\n", info.free_size / 1024);
        printf("  Usage: %.1f%%\n", (float)info.used_size * 100 / info.total_size);
    }
    
    // 检查存储空间
    if (info.free_size < MIN_FREE_SPACE) {
        printf("Storage space low, cleaning up...\n");
        
        // 清理7天前的数据
        int cleaned = data_storage_cleanup(STORAGE_TYPE_FLASH, 7);
        printf("Cleaned %d bytes\n", cleaned);
    }
    
    // 同步缓存数据
    data_storage_sync();
}
```

## 📈 存储策略

### 分级存储策略

```c
// 数据重要性分级
typedef enum {
    DATA_PRIORITY_CRITICAL = 0,  // 关键数据 - 内部Flash
    DATA_PRIORITY_HIGH = 1,      // 重要数据 - 外部Flash
    DATA_PRIORITY_NORMAL = 2,    // 普通数据 - SD卡
    DATA_PRIORITY_LOW = 3        // 临时数据 - RAM缓存
} data_priority_t;

// 根据数据重要性选择存储介质
storage_type_t select_storage_by_priority(data_priority_t priority) {
    switch (priority) {
        case DATA_PRIORITY_CRITICAL:
            return STORAGE_TYPE_INTERNAL_FLASH;
        case DATA_PRIORITY_HIGH:
            return STORAGE_TYPE_EXTERNAL_FLASH;
        case DATA_PRIORITY_NORMAL:
            return STORAGE_TYPE_SD_CARD;
        case DATA_PRIORITY_LOW:
        default:
            return STORAGE_TYPE_RAM_CACHE;
    }
}
```

### 循环覆盖策略

```c
// 循环缓冲区管理
typedef struct {
    uint32_t write_index;       // 写入索引
    uint32_t read_index;        // 读取索引
    uint32_t total_size;        // 总大小
    uint32_t record_size;       // 记录大小
    uint8_t* buffer;            // 缓冲区指针
} circular_buffer_t;

// 循环写入数据
int circular_write(circular_buffer_t* cb, const void* data) {
    // 计算写入位置
    uint32_t write_pos = (cb->write_index * cb->record_size) % cb->total_size;
    
    // 写入数据
    memcpy(cb->buffer + write_pos, data, cb->record_size);
    
    // 更新索引
    cb->write_index++;
    
    // 检查是否覆盖了读取位置
    if (cb->write_index - cb->read_index > cb->total_size / cb->record_size) {
        cb->read_index = cb->write_index - cb->total_size / cb->record_size;
    }
    
    return 0;
}
```

### 压缩存储策略

```c
// 数据压缩配置
#define COMPRESSION_THRESHOLD   1024    // 压缩阈值
#define COMPRESSION_RATIO       0.7f    // 期望压缩比

// 智能压缩存储
int compressed_storage_write(const void* data, uint32_t size) {
    if (size > COMPRESSION_THRESHOLD) {
        // 大数据进行压缩
        uint8_t* compressed_data = malloc(size);
        uint32_t compressed_size = compress_data(data, size, compressed_data);
        
        if (compressed_size < size * COMPRESSION_RATIO) {
            // 压缩效果好，存储压缩数据
            int result = storage_write_compressed(compressed_data, compressed_size);
            free(compressed_data);
            return result;
        } else {
            // 压缩效果差，存储原始数据
            free(compressed_data);
            return storage_write_raw(data, size);
        }
    } else {
        // 小数据直接存储
        return storage_write_raw(data, size);
    }
}
```
